import React, { useEffect, useState } from 'react';
import { <PERSON>ton, Card, Typography, Space, Alert, Tag } from 'antd';

const { Title, Paragraph, Text } = Typography;

/**
 * Demo component to showcase automatic Picture-in-Picture functionality
 * This component provides testing controls and status information for auto PiP
 */
export function AutoPipDemo() {
  const [autoPipStatus, setAutoPipStatus] = useState(null);
  const [isPageHidden, setIsPageHidden] = useState(false);

  // Update status from window.daakiaAutoPip
  const updateStatus = () => {
    if (window.daakiaAutoPip) {
      setAutoPipStatus(window.daakiaAutoPip.status());
    }
  };

  // Monitor page visibility for demo
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsPageHidden(document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Update status periodically
    const interval = setInterval(updateStatus, 1000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(interval);
    };
  }, []);

  const handleToggleAutoPip = () => {
    if (window.daakiaAutoPip) {
      window.daakiaAutoPip.toggle(!autoPipStatus?.enabled);
      updateStatus();
    }
  };

  const handleRequestPermission = async () => {
    if (window.daakiaAutoPip) {
      await window.daakiaAutoPip.requestPermission();
      updateStatus();
    }
  };

  const handleTriggerManual = async () => {
    if (window.daakiaAutoPip) {
      await window.daakiaAutoPip.trigger();
      updateStatus();
    }
  };

  const getPermissionColor = (permission) => {
    switch (permission) {
      case 'granted': return 'green';
      case 'denied': return 'red';
      case 'prompt': return 'orange';
      default: return 'default';
    }
  };

  return (
    <Card title="Automatic Picture-in-Picture Demo" style={{ maxWidth: 600, margin: '20px auto' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        <Alert
          message="How to Test Automatic PiP"
          description={
            <div>
              <p>1. Join a video conference with other participants</p>
              <p>2. Interact with the page (click anywhere)</p>
              <p>3. Switch to another tab or minimize the window</p>
              <p>4. PiP should automatically open after 1 second</p>
              <p>5. Return to this tab to see PiP auto-close</p>
            </div>
          }
          type="info"
          showIcon
        />

        {autoPipStatus && (
          <Card size="small" title="Auto PiP Status">
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div>
                <Text strong>Browser Support: </Text>
                <Tag color={autoPipStatus.supported ? 'green' : 'red'}>
                  {autoPipStatus.supported ? 'Supported' : 'Not Supported'}
                </Tag>
              </div>
              
              <div>
                <Text strong>Auto PiP Enabled: </Text>
                <Tag color={autoPipStatus.enabled ? 'green' : 'red'}>
                  {autoPipStatus.enabled ? 'Enabled' : 'Disabled'}
                </Tag>
              </div>
              
              <div>
                <Text strong>Permission: </Text>
                <Tag color={getPermissionColor(autoPipStatus.permission)}>
                  {autoPipStatus.permission}
                </Tag>
              </div>
              
              <div>
                <Text strong>Eligible for Auto PiP: </Text>
                <Tag color={autoPipStatus.eligible ? 'green' : 'orange'}>
                  {autoPipStatus.eligible ? 'Yes' : 'No'}
                </Tag>
              </div>
              
              <div>
                <Text strong>Page Visibility: </Text>
                <Tag color={isPageHidden ? 'red' : 'green'}>
                  {isPageHidden ? 'Hidden' : 'Visible'}
                </Tag>
              </div>
            </Space>
          </Card>
        )}

        <Space wrap>
          <Button 
            type="primary" 
            onClick={handleToggleAutoPip}
            disabled={!autoPipStatus?.supported}
          >
            {autoPipStatus?.enabled ? 'Disable' : 'Enable'} Auto PiP
          </Button>
          
          <Button 
            onClick={handleRequestPermission}
            disabled={!autoPipStatus?.supported || autoPipStatus?.permission !== 'prompt'}
          >
            Request Permission
          </Button>
          
          <Button 
            onClick={handleTriggerManual}
            disabled={!autoPipStatus?.eligible}
          >
            Trigger PiP Manually
          </Button>
          
          <Button onClick={updateStatus}>
            Refresh Status
          </Button>
        </Space>

        <Card size="small" title="Browser Compatibility">
          <Paragraph>
            <Text strong>Supported Browsers:</Text>
            <ul>
              <li>Chrome 116+ (Desktop)</li>
              <li>Edge 116+ (Desktop)</li>
              <li>Opera 102+ (Desktop)</li>
            </ul>
          </Paragraph>
          
          <Paragraph>
            <Text strong>Required APIs:</Text>
            <ul>
              <li>Document Picture-in-Picture API</li>
              <li>Page Visibility API</li>
              <li>Media Session API (optional, for browser controls)</li>
            </ul>
          </Paragraph>
        </Card>

        <Alert
          message="Debug Console"
          description={
            <div>
              <p>Open browser console and use:</p>
              <code>window.daakiaAutoPip.status()</code> - Check current status<br/>
              <code>window.daakiaAutoPip.trigger()</code> - Manually trigger PiP<br/>
              <code>window.togglePipControlPosition()</code> - Toggle control position
            </div>
          }
          type="warning"
        />

      </Space>
    </Card>
  );
}

export default AutoPipDemo;
