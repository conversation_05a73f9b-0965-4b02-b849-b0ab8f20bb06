import { useRef, useState, useCallback, useMemo, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { VideoTrack, useTracks, isTrackReference, useParticipants, TrackToggle, DisconnectButton } from '@livekit/components-react';
import { PhoneOutlined } from '@ant-design/icons';
import { generateAvatar, parseMetadata } from '../utils/helper';
import { ScreenCaptureButton } from '../components/settings/ScreenCapture/ScreenCaptureButton';
import './PictureInPicture.scss';

// Speaking priority configuration
const SPEAKING_PRIORITY_CONFIG = {
  STICKY_DURATION: 5000, // Keep speaking participants visible for 5 seconds after they stop speaking
  UPDATE_INTERVAL: 500,   // Check for speaking changes every 500ms
};

function SimplePipContent({
  localParticipant,
  controlPosition = 'bottom',
  room,
  setToastNotification,
  setToastStatus,
  setShowToast,
  isHost,
  isCoHost
}) {

  const allParticipants = useParticipants();

  // State for speaking priority tracking
  const [speakingHistory, setSpeakingHistory] = useState(new Map());

  // State for expanded participants view
  const [showExpandedView, setShowExpandedView] = useState(false);
  const [expandedViewPage, setExpandedViewPage] = useState(0);

  // Get camera and screen share tracks for video display
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
    { source: Track.Source.ScreenShare, withPlaceholder: false },
  ]);

  // Find local camera track
  const localCameraTrack = allTracks
    .filter(isTrackReference)
    .find((track) =>
      track.participant.isLocal &&
      track.source === Track.Source.Camera
    );


  const screenShareTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => track.publication.source === Track.Source.ScreenShare);


  const isScreenShareActive = screenShareTracks.some((track) =>
    track.publication.isSubscribed && !track.publication.isMuted
  );

  const activeScreenShareTrack = screenShareTracks.find((track) =>
    track.publication.isSubscribed && !track.publication.isMuted
  );


  useEffect(() => {
    const now = Date.now();
    const currentSpeaking = allParticipants.filter(p => p.isSpeaking);

    if (currentSpeaking.length > 0) {
      setSpeakingHistory(prev => {
        const updated = new Map(prev);
        currentSpeaking.forEach(p => {
          updated.set(p.identity, now);
        });
        return updated;
      });
    }
  }, [allParticipants.map(p => `${p.identity}-${p.isSpeaking}`).join(',')]);

  // Advanced Speaking Priority System with Sticky Behavior
  const getAdvancedSpeakingPriorityParticipants = useCallback((participants, maxCount) => {
    const now = Date.now();
    const currentSpeaking = participants.filter(p => p.isSpeaking);
    const currentSpeakingIds = new Set(currentSpeaking.map(p => p.identity));

    // Get recently speaking participants (within sticky duration)
    const recentlySpeaking = participants.filter(p => {
      const lastSpeakingTime = speakingHistory.get(p.identity);
      return lastSpeakingTime && (now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION;
    });

    // Priority order: Currently speaking > Recently speaking > Others
    const currentlySpakingParticipants = participants.filter(p => currentSpeakingIds.has(p.identity));
    const recentlySpokingParticipants = recentlySpeaking.filter(p => !currentSpeakingIds.has(p.identity));
    const otherParticipants = participants.filter(p =>
      !currentSpeakingIds.has(p.identity) &&
      !recentlySpeaking.some(rp => rp.identity === p.identity)
    );

    // Build final list with priority
    const prioritizedList = [
      ...currentlySpakingParticipants,
      ...recentlySpokingParticipants,
      ...otherParticipants
    ];

    // Return up to maxCount participants
    return prioritizedList.slice(0, maxCount);
  }, [speakingHistory]);

  // Periodic update for speaking priority (cleanup old speaking history)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setSpeakingHistory(prev => {
        const updated = new Map();
        prev.forEach((lastSpeakingTime, participantId) => {
          // Keep only recent speaking history
          if ((now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION * 2) {
            updated.set(participantId, lastSpeakingTime);
          }
        });
        return updated;
      });
    }, SPEAKING_PRIORITY_CONFIG.UPDATE_INTERVAL);

    return () => clearInterval(interval);
  }, [setSpeakingHistory]);

  // Get remote participants with advanced speaking priority
  // When screen sharing: max 2 remotes, when no screen share: max 3 remotes
  const maxRemotes = isScreenShareActive ? 2 : 3;
  const allRemoteParticipants = allParticipants.filter((participant) => !participant.isLocal);
  const remoteParticipants = getAdvancedSpeakingPriorityParticipants(allRemoteParticipants, maxRemotes);

  // Calculate left-out participants (those not shown in main view)
  const leftOutParticipants = allRemoteParticipants.filter(
    (participant) => !remoteParticipants.some(rp => rp.identity === participant.identity)
  );

  // Pagination for expanded view (max 9 participants per page)
  const PARTICIPANTS_PER_PAGE = 9;
  const totalPages = Math.ceil(leftOutParticipants.length / PARTICIPANTS_PER_PAGE);
  const startIndex = expandedViewPage * PARTICIPANTS_PER_PAGE;
  const endIndex = startIndex + PARTICIPANTS_PER_PAGE;
  const currentPageParticipants = leftOutParticipants.slice(startIndex, endIndex);

  // Create remote participant data with their camera tracks (if any)
  const remoteParticipantsWithTracks = remoteParticipants.map((participant) => {
    const cameraTrack = allTracks
      .filter(isTrackReference)
      .find((track) =>
        track.participant.identity === participant.identity &&
        track.source === Track.Source.Camera
      );

    return {
      participant,
      track: cameraTrack || null
    };
  });

  // Create expanded view participant data with tracks
  const expandedParticipantsWithTracks = currentPageParticipants.map((participant) => {
    const cameraTrack = allTracks
      .filter(isTrackReference)
      .find((track) =>
        track.participant.identity === participant.identity &&
        track.source === Track.Source.Camera
      );

    return {
      participant,
      track: cameraTrack || null
    };
  });

  // Check if local participant is speaking
  const isSpeaking = localParticipant?.isSpeaking || false;

  // Get participant count for dynamic grid (now based on all remote participants)
  const participantCount = remoteParticipants.length;

  // Dynamic grid class based on participant count, screen share status, and control position
  const getGridClass = (count, controlPos, hasScreenShare) => {
    const baseClass = controlPos === 'top' ? 'control-top' : 'control-bottom';
    const screenSharePrefix = hasScreenShare ? 'pip-grid-screenshare' : 'pip-grid';

    if (hasScreenShare) {
      // WITH SCREEN SHARE layouts
      switch(count) {
        case 0: return `${screenSharePrefix}-solo ${baseClass}`;
        case 1: return `${screenSharePrefix}-two ${baseClass}`;
        case 2: return `${screenSharePrefix}-three ${baseClass}`;
        default: return `${screenSharePrefix}-three ${baseClass}`;
      }
    } else {
      // NO SCREEN SHARE layouts (your existing perfect logic)
      switch(count) {
        case 0: return `${screenSharePrefix}-solo ${baseClass}`;
        case 1: return `${screenSharePrefix}-two ${baseClass}`;
        case 2: return `${screenSharePrefix}-three ${baseClass}`;
        case 3: return `${screenSharePrefix}-four ${baseClass}`;
        default: return `${screenSharePrefix}-four ${baseClass}`;
      }
    }
  };

  // Get expanded view grid class based on participant count and control position
  const getExpandedGridClass = (count, controlPos) => {
    const baseClass = controlPos === 'top' ? 'control-top' : 'control-bottom';
    if (count <= 1) return `pip-expanded-grid-1 ${baseClass}`;
    if (count <= 4) return `pip-expanded-grid-4 ${baseClass}`;
    if (count <= 6) return `pip-expanded-grid-6 ${baseClass}`;
    return `pip-expanded-grid-9 ${baseClass}`;
  };

  // Navigation functions for expanded view
  const goToNextPage = () => {
    if (expandedViewPage < totalPages - 1) {
      setExpandedViewPage(expandedViewPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (expandedViewPage > 0) {
      setExpandedViewPage(expandedViewPage - 1);
    }
  };

  const toggleExpandedView = () => {
    setShowExpandedView(!showExpandedView);
    setExpandedViewPage(0); // Reset to first page when toggling
  };

  // Auto-return to normal view when no left-out participants
  useEffect(() => {
    if (showExpandedView && leftOutParticipants.length === 0) {
      setShowExpandedView(false);
      setExpandedViewPage(0);
    }
  }, [showExpandedView, leftOutParticipants.length]);


  const avatarName = localParticipant?.name
    ? generateAvatar(localParticipant.name)
    : generateAvatar(localParticipant.identity);
  let avatarColor = '#7C4DFF';
  try {
    const metaColor = parseMetadata(localParticipant?.metadata)?.color;
    if (metaColor) avatarColor = metaColor;
  } catch (e) { /* ignore */ }


  const getRemoteParticipantInfo = (participant) => {
    const name = participant?.name
      ? generateAvatar(participant.name)
      : generateAvatar(participant.identity);
    let color = '#7C4DFF';
    try {
      const metaColor = parseMetadata(participant?.metadata)?.color;
      if (metaColor) color = metaColor;
    } catch (e) { /* ignore */ }
    return { name, color };
  };


  const ControlTile = useMemo(() => {
    return (
      <div className="pip-tile pip-control-tile">
        <div className="pip-control-content">
          <div className="pip-control-buttons">
            {showExpandedView ? (
              // EXPANDED VIEW CONTROLS: Back, Previous/Next, Disconnect
              <>
                <button
                  className="pip-control-button pip-nav-button"
                  onClick={() => setShowExpandedView(false)}
                >
                  ←
                </button>
                {totalPages > 1 && (
                  <>
                    <button
                      className="pip-control-button pip-nav-button"
                      onClick={goToPrevPage}
                      disabled={expandedViewPage === 0}
                    >
                      ‹
                    </button>
                    <div className="pip-control-page-info">
                      {expandedViewPage + 1}/{totalPages}
                    </div>
                    <button
                      className="pip-control-button pip-nav-button"
                      onClick={goToNextPage}
                      disabled={expandedViewPage >= totalPages - 1}
                    >
                      ›
                    </button>
                  </>
                )}
                <DisconnectButton
                  className="pip-control-button pip-disconnect-button"
                >
                  <PhoneOutlined />
                </DisconnectButton>
              </>
            ) : (
              // NORMAL VIEW CONTROLS: Mic, Camera, Screen Share, Participants Count, Disconnect
              <>
                <TrackToggle
                  source={Track.Source.Microphone}
                  showIcon
                  className="pip-control-button"
                />
                <TrackToggle
                  source={Track.Source.Camera}
                  showIcon
                  className="pip-control-button"
                />
                {/* Only show screen share button when screen sharing is active */}
                {isScreenShareActive && (
                  <TrackToggle
                    source={Track.Source.ScreenShare}
                    showIcon
                    className="pip-control-button"
                  />
                )}
                {/* Show participants count button if there are left-out participants */}
                {leftOutParticipants.length > 0 && (
                  <button
                    className="pip-control-button pip-participants-button"
                    onClick={toggleExpandedView}
                  >
                    +{leftOutParticipants.length}
                  </button>
                )}
                <DisconnectButton
                  className="pip-control-button pip-disconnect-button"
                >
                  <PhoneOutlined />
                </DisconnectButton>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }, [showExpandedView, isScreenShareActive, leftOutParticipants.length, toggleExpandedView, totalPages, expandedViewPage, goToPrevPage, goToNextPage]); // Add all dependencies

  return (
    <div className="pip-main-container">
      {/* Show expanded view or normal view */}
      {showExpandedView ? (
        // EXPANDED VIEW: Show left-out participants
        <div className={`pip-grid-container ${getExpandedGridClass(currentPageParticipants.length, controlPosition)}`}>
          {/* Control tile - positioned at top if controlPosition is 'top' */}
          {controlPosition === 'top' && ControlTile}
            {expandedParticipantsWithTracks.map((participantData, index) => {
              const { participant, track } = participantData;
              const remoteInfo = getRemoteParticipantInfo(participant);
              const isRemoteSpeaking = participant?.isSpeaking || false;

              return (
                <div
                  key={participant.identity || `expanded-${index}`}
                  className={`pip-tile pip-tile-expanded ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
                >
                  {track?.publication && !track.publication.isMuted ? (
                    // Show remote video
                    <div className="pip-video-container">
                      <VideoTrack
                        trackRef={track}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain',
                          borderRadius: '2vmin'
                        }}
                      />
                    </div>
                  ) : (
                    // Show remote avatar when camera is off
                    <div className="pip-tile-avatar-center">
                      <div
                        className="pip-tile-avatar pip-tile-avatar-expanded"
                        style={{ backgroundColor: remoteInfo.color }}
                      >
                        {remoteInfo.name}
                      </div>
                    </div>
                  )}

                  {/* Corner accent lights for remote speaking */}
                  {isRemoteSpeaking && (
                    <>
                      <div style={{
                        position: 'absolute',
                        bottom: '0.5vmin',
                        left: '0.5vmin',
                        width: '1vmin',
                        height: '1vmin',
                        background: '#1e8cfa',
                        borderRadius: '50%',
                        zIndex: 2,
                        animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        animationDelay: '0.6s'
                      }} />
                      <div style={{
                        position: 'absolute',
                        bottom: '0.5vmin',
                        right: '0.5vmin',
                        width: '1vmin',
                        height: '1vmin',
                        background: '#1e8cfa',
                        borderRadius: '50%',
                        zIndex: 2,
                        animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        animationDelay: '0.9s'
                      }} />
                    </>
                  )}
                </div>
              );
            })}

          {/* Control tile for expanded view */}
          {controlPosition === 'bottom' && ControlTile}
        </div>
      ) : (
        // NORMAL VIEW: Show main participants
        <div className={`pip-grid-container ${getGridClass(participantCount, controlPosition, isScreenShareActive)}`}>




        {isScreenShareActive ? (
          <>            {/* Screen Share Tile - Always the big tile on top */}
            <div className="pip-tile pip-tile-screenshare">
              {activeScreenShareTrack ? (
                <div className="pip-video-container">
                  <VideoTrack
                    trackRef={activeScreenShareTrack}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      borderRadius: '2vmin'
                    }}
                  />
                  
                  {(isHost || isCoHost) && (
                    <div className="pip-screen-capture-button">
                      <ScreenCaptureButton
                        room={room}
                        screenShareTracks={screenShareTracks}
                        focusTrack={activeScreenShareTrack}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setShowPopover={() => {}} // No popover in PiP
                      />
                    </div>
                  )}
                </div>
              ) : (
                <div className="pip-tile-center-dot" />
              )}
            </div>

            {/* Local Participant Tile - Below screen share */}
            {/* MEMORY OPTIMIZATION: When screen sharing, show only avatar for local participant (no video) */}
            <div className={`pip-tile pip-tile-small pip-tile-local ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
              {/* Always show avatar for local participant when screen sharing (memory optimization) */}
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: avatarColor }}
                >
                  {avatarName}
                </div>
              </div>
              {isSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          </>
        ) : (
          // NO SCREEN SHARE: Local participant as main tile
          <div className={`pip-tile pip-tile-main ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
            {localCameraTrack?.publication && !localCameraTrack.publication.isMuted ? (
              // Show video when camera is on
              <div className="pip-video-container">
                <VideoTrack
                  trackRef={localCameraTrack}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    borderRadius: '2vmin'
                  }}
                />
              </div>
            ) : (
              // Show avatar when camera is off
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: avatarColor }}
                >
                  {avatarName}
                </div>
              </div>
            )}

            {/* Corner accent lights for local speaking */}
            {isSpeaking && (
              <>
                <div style={{
                  position: 'absolute',
                  bottom: '1vmin',
                  left: '1vmin',
                  width: '1.5vmin',
                  height: '1.5vmin',
                  background: '#1e8cfa',
                  borderRadius: '50%',
                  zIndex: 2,
                  animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                  animationDelay: '0.6s'
                }} />
                <div style={{
                  position: 'absolute',
                  bottom: '1vmin',
                  right: '1vmin',
                  width: '1.5vmin',
                  height: '1.5vmin',
                  background: '#1e8cfa',
                  borderRadius: '50%',
                  zIndex: 2,
                  animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                  animationDelay: '0.9s'
                }} />
              </>
            )}
          </div>
        )}

        {/* Dynamic remote participant tiles - Only show when NO screen share */}
        {!isScreenShareActive && remoteParticipantsWithTracks.map((participantData, index) => {
          const { participant, track } = participantData;
          const remoteInfo = getRemoteParticipantInfo(participant);
          const isRemoteSpeaking = participant?.isSpeaking || false;

          return (
            <div
              key={participant.identity || `remote-${index}`}
              className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
            >
              {track?.publication && !track.publication.isMuted ? (
                // Show remote video
                <div className="pip-video-container">
                  <VideoTrack
                    trackRef={track}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      borderRadius: '2vmin'
                    }}
                  />
                </div>
              ) : (
                // Show remote avatar when camera is off
                <div className="pip-tile-avatar-center">
                  <div
                    className="pip-tile-avatar pip-tile-avatar-small"
                    style={{ backgroundColor: remoteInfo.color }}
                  >
                    {remoteInfo.name}
                  </div>
                </div>
              )}

              {/* Corner accent lights for remote speaking */}
              {isRemoteSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          );
        })}

        {/* Dynamic remote participant tiles - Only show when WITH screen share */}
        {/* MEMORY OPTIMIZATION: When screen sharing, show only avatars for remote participants */}
        {isScreenShareActive && remoteParticipantsWithTracks.map((participantData, index) => {
          const { participant } = participantData;
          const remoteInfo = getRemoteParticipantInfo(participant);
          const isRemoteSpeaking = participant?.isSpeaking || false;

          return (
            <div
              key={participant.identity || `remote-screenshare-${index}`}
              className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
            >
              {/* Always show avatar for remote participants when screen sharing (memory optimization) */}
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: remoteInfo.color }}
                >
                  {remoteInfo.name}
                </div>
              </div>

              {/* Corner accent lights for remote speaking */}
              {isRemoteSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          );
        })}

          {/* Control tile - positioned at bottom if controlPosition is 'bottom' */}
          {controlPosition === 'bottom' && ControlTile}
        </div>
      )}
    </div>
  );
}

export function usePictureInPicture({
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  localParticipant,
  room,
  controlPosition = 'top',
  isHost,
  isCoHost,
  enableAutoPip = true // New prop to enable/disable automatic PiP
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Auto PiP state management
  const [autoPipEnabled, setAutoPipEnabled] = useState(enableAutoPip);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [autoPipPermission, setAutoPipPermission] = useState('prompt'); // 'granted', 'denied', 'prompt'
  const visibilityChangeTimeoutRef = useRef(null);
  const lastVisibilityChangeRef = useRef(Date.now());

  // Get participant count for dynamic sizing
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
  ]);
  const remoteTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => !track.participant.isLocal);
  const participantCount = remoteTracks.length;

  // Dynamic PiP window configuration based on participant count
  const getDynamicConfig = (count) => {
    switch(count) {
      case 0: // Solo - minimum size
        return { width: 180, height: 297 };  // Increased from 270
      case 1: // Two participants - small window
        return { width: 220, height: 363 };  // Increased from 330
      case 2: // Three participants - medium window
        return { width: 280, height: 429 };  // Increased from 390
      case 3: // Four participants - larger window
        return { width: 320, height: 495 };  // Increased from 450
      default:
        return { width: 320, height: 528 };  // Increased from 480
    }
  };

  const defaultConfig = useMemo(() => getDynamicConfig(participantCount), [participantCount]);

  // Track previous participant count for notifications
  const prevParticipantCountRef = useRef(participantCount);

  // Auto-resize PiP window when participant count changes
  useEffect(() => {
    if (pipWindowRef.current && pipWindowDocument) {
      const newConfig = getDynamicConfig(participantCount);
      const prevCount = prevParticipantCountRef.current;

      try {
        // Resize the existing PiP window
        pipWindowRef.current.resizeTo(newConfig.width, newConfig.height);

        // Show notification about participant change (only if PiP is active)
        if (prevCount !== participantCount && prevCount !== undefined) {
          const participantChange = participantCount > prevCount ? 'joined' : 'left';
          const totalParticipants = participantCount + 1; // +1 for local participant

          setToastNotification(`Participant ${participantChange}. PiP adjusted for ${totalParticipants} participant${totalParticipants > 1 ? 's' : ''}.`);
          setToastStatus("info");
          setShowToast(true);
        }

      } catch (error) {
        console.log('PiP resize not supported on this browser');
      }
    }

    // Update previous count
    prevParticipantCountRef.current = participantCount;
  }, [participantCount, pipWindowDocument, setToastNotification, setToastStatus, setShowToast]);

  // Check Document PiP support and browser capabilities
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  const isPageVisibilitySupported = useMemo(() => {
    return typeof document.hidden !== 'undefined' && typeof document.visibilityState !== 'undefined';
  }, []);

  const isMediaSessionSupported = useMemo(() => {
    return 'mediaSession' in navigator && 'setActionHandler' in navigator.mediaSession;
  }, []);

  // Check if automatic PiP is eligible based on conditions
  const isAutoPipEligible = useMemo(() => {
    console.log('🔍 Auto PiP Eligibility Check:', {
      autoPipEnabled,
      isSupported,
      isPageVisibilitySupported,
      participantCount,
      roomState: room?.state,
      hasUserInteracted,
      localParticipant: !!localParticipant
    });

    if (!autoPipEnabled) {
      console.log('❌ Auto PiP not eligible: Auto PiP is disabled');
      return false;
    }

    if (!isSupported) {
      console.log('❌ Auto PiP not eligible: Document PiP not supported');
      return false;
    }

    if (!isPageVisibilitySupported) {
      console.log('❌ Auto PiP not eligible: Page Visibility API not supported');
      return false;
    }

    // Check if we're in a video conference context (has participants)
    const hasParticipants = participantCount > 0 || (room && room.participants && room.participants.size > 1);
    if (!hasParticipants) {
      console.log('❌ Auto PiP not eligible: No other participants in meeting');
      return false;
    }

    // Check if user has interacted with the page (for user activation requirement)
    const hasActivation = hasUserInteracted;
    if (!hasActivation) {
      console.log('❌ Auto PiP not eligible: User has not interacted with page yet');
      return false;
    }

    // Check if we're actively in a meeting
    const isInMeeting = room && room.state === 'connected' && localParticipant;
    if (!isInMeeting) {
      console.log('❌ Auto PiP not eligible: Not in active meeting (room state:', room?.state, ', localParticipant:', !!localParticipant, ')');
      return false;
    }

    console.log('✅ Auto PiP is eligible!');
    return true;
  }, [autoPipEnabled, isSupported, isPageVisibilitySupported, participantCount, room, hasUserInteracted, localParticipant]);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Simple PiP Content
  const PipContent = useCallback(() => {
    return <SimplePipContent
      localParticipant={localParticipant}
      controlPosition={controlPosition}
      room={room}
      setToastNotification={setToastNotification}
      setToastStatus={setToastStatus}
      setShowToast={setShowToast}
      isHost={isHost}
      isCoHost={isCoHost}
    />;
  }, [localParticipant, controlPosition, room, setToastNotification, setToastStatus, setShowToast, isHost, isCoHost]);




  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Request user permission for automatic PiP
  const requestAutoPipPermission = useCallback(async () => {
    console.log('🔔 Requesting auto PiP permission from user...');

    return new Promise((resolve) => {
      // Create a simple permission dialog
      const shouldEnable = window.confirm(
        "Allow automatic Picture-in-Picture when you switch tabs?\n\n" +
        "This will help you stay connected to your video conference while using other applications."
      );

      console.log('👤 User permission response:', shouldEnable ? 'GRANTED' : 'DENIED');

      if (shouldEnable) {
        setAutoPipPermission('granted');
        // Store preference in localStorage
        localStorage.setItem('daakia-auto-pip-permission', 'granted');
        console.log('💾 Auto PiP permission saved as granted');
        setToastNotification("Automatic Picture-in-Picture enabled");
        setToastStatus("success");
        setShowToast(true);
      } else {
        setAutoPipPermission('denied');
        localStorage.setItem('daakia-auto-pip-permission', 'denied');
        console.log('💾 Auto PiP permission saved as denied');
      }

      resolve(shouldEnable);
    });
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async (isAutoTriggered = false) => {
    console.log('📺 Opening PiP window...', { isAutoTriggered, isSupported });

    if (!isSupported) {
      console.log('❌ PiP window failed: Document PiP not supported');
      if (!isAutoTriggered) {
        handlePipError(new Error('Document Picture-in-Picture not supported'));
      }
      return false;
    }

    if (pipWindowRef.current) {
      console.log('ℹ️ PiP window already exists');
      return true;
    }

    try {
      console.log('🔧 Requesting PiP window with config:', defaultConfig);

      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      console.log('✅ PiP window created successfully');

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Show notification for auto-triggered PiP
      if (isAutoTriggered) {
        console.log('🔔 Showing auto PiP notification');
        setToastNotification("Picture-in-Picture activated automatically");
        setToastStatus("info");
        setShowToast(true);
      }

      // Setup document and copy styles from main document
      const pipDoc = pipWindow.document;

      // Copy all stylesheets from the main document to PiP window
      const mainStyleSheets = Array.from(document.styleSheets);
      mainStyleSheets.forEach((styleSheet) => {
        try {
          if (styleSheet.href) {
            // External stylesheet
            const link = pipDoc.createElement('link');
            link.rel = 'stylesheet';
            link.href = styleSheet.href;
            pipDoc.head.appendChild(link);
          } else if (styleSheet.ownerNode) {
            // Inline stylesheet
            const clonedStyle = styleSheet.ownerNode.cloneNode(true);
            pipDoc.head.appendChild(clonedStyle);
          }
        } catch (e) {
          // Handle CORS issues with external stylesheets
          console.warn('Could not copy stylesheet:', e);
        }
      });

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      if (!isAutoTriggered) {
        handlePipError(error);
      } else {
        console.warn('Auto PiP failed:', error.message);
      }
      return false;
    }
  }, [isSupported, defaultConfig, setIsPIPEnabled, closePipWindow, handlePipError, setToastNotification, setToastStatus, setShowToast]);

  // Auto PiP trigger function
  const triggerAutoPip = useCallback(async () => {
    console.log('🚀 Auto PiP trigger called');

    if (!isAutoPipEligible) {
      console.log('❌ Auto PiP trigger failed: Not eligible');
      return false;
    }

    // Check permission state
    if (autoPipPermission === 'denied') {
      console.log('❌ Auto PiP trigger failed: Permission denied');
      return false;
    }

    // If permission is prompt, ask user
    if (autoPipPermission === 'prompt') {
      console.log('🔔 Auto PiP requesting user permission...');
      const userConsent = await requestAutoPipPermission();
      if (!userConsent) {
        console.log('❌ Auto PiP trigger failed: User denied permission');
        return false;
      }
      console.log('✅ Auto PiP permission granted by user');
    }

    console.log('📺 Auto PiP opening window...');
    return openPipWindow(true); // Pass true to indicate this is an auto-trigger
  }, [isAutoPipEligible, autoPipPermission, requestAutoPipPermission, openPipWindow]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  // Page Visibility API integration for automatic PiP
  useEffect(() => {
    if (!isPageVisibilitySupported || !autoPipEnabled) return;

    const handleVisibilityChange = () => {
      const now = Date.now();
      lastVisibilityChangeRef.current = now;

      console.log('👁️ Page visibility changed:', {
        hidden: document.hidden,
        visibilityState: document.visibilityState,
        timestamp: new Date().toISOString()
      });

      // Clear any existing timeout
      if (visibilityChangeTimeoutRef.current) {
        console.log('⏰ Clearing existing visibility timeout');
        clearTimeout(visibilityChangeTimeoutRef.current);
      }

      if (document.hidden) {
        // Page became hidden (user switched tabs or minimized window)
        console.log('🙈 Page became hidden - starting auto PiP timer...');

        // Add a small delay to avoid rapid tab switching triggering PiP
        visibilityChangeTimeoutRef.current = setTimeout(() => {
          console.log('⏰ Auto PiP timer expired, checking conditions...');
          console.log('📊 Current state:', {
            stillHidden: document.hidden,
            isEligible: isAutoPipEligible,
            hasPipWindow: !!pipWindowRef.current
          });

          if (document.hidden && isAutoPipEligible && !pipWindowRef.current) {
            console.log('🚀 Triggering auto PiP...');
            triggerAutoPip();
          } else {
            console.log('❌ Auto PiP not triggered - conditions not met');
          }
        }, 1000); // 1 second delay
      } else if (pipWindowRef.current) {
        // Page became visible again - Auto-close PiP when returning to main tab
        console.log('👀 Page became visible - closing auto PiP...');
        closePipWindow();
        setToastNotification("Picture-in-Picture closed - welcome back!");
        setToastStatus("info");
        setShowToast(true);
      } else {
        console.log('👀 Page became visible - no PiP to close');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (visibilityChangeTimeoutRef.current) {
        clearTimeout(visibilityChangeTimeoutRef.current);
      }
    };
  }, [isPageVisibilitySupported, autoPipEnabled, isAutoPipEligible, triggerAutoPip, closePipWindow, setToastNotification, setToastStatus, setShowToast]);

  // Media Session API integration for browser controls
  useEffect(() => {
    if (!isMediaSessionSupported || !autoPipEnabled) return;

    try {
      // Register the enterpictureinpicture action handler
      navigator.mediaSession.setActionHandler('enterpictureinpicture', async () => {
        if (isAutoPipEligible) {
          await triggerAutoPip();
        }
      });

      // Set basic metadata for media session
      navigator.mediaSession.metadata = new MediaMetadata({
        title: 'Daakia Video Conference',
        artist: 'Video Call in Progress',
        artwork: [
          { src: '/favicon.ico', sizes: '96x96', type: 'image/x-icon' }
        ]
      });

    } catch (error) {
      console.warn('Media Session API setup failed:', error);
    }

    return () => {
      try {
        navigator.mediaSession.setActionHandler('enterpictureinpicture', null);
      } catch (error) {
        console.warn('Media Session cleanup failed:', error);
      }
    };
  }, [isMediaSessionSupported, autoPipEnabled, isAutoPipEligible, triggerAutoPip]);

  // Track user interaction for activation requirement
  useEffect(() => {
    const handleUserInteraction = (event) => {
      if (!hasUserInteracted) {
        console.log('👆 User interaction detected:', event.type);
        setHasUserInteracted(true);
      }
    };

    // Listen for various user interaction events
    const events = ['click', 'keydown', 'touchstart', 'mousedown'];
    console.log('👂 Setting up user interaction listeners for auto PiP...');

    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true, passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  }, [hasUserInteracted]);

  // Load saved auto PiP permission on mount
  useEffect(() => {
    const savedPermission = localStorage.getItem('daakia-auto-pip-permission');
    console.log('💾 Loading saved auto PiP permission:', savedPermission || 'none');

    if (savedPermission && ['granted', 'denied'].includes(savedPermission)) {
      setAutoPipPermission(savedPermission);
      console.log('✅ Auto PiP permission loaded:', savedPermission);
    } else {
      console.log('ℹ️ No saved auto PiP permission, will prompt user');
    }
  }, []);

  // Debug logging for auto PiP initialization
  useEffect(() => {
    console.log('🔧 Auto PiP Hook Initialized:', {
      enableAutoPip,
      isSupported,
      isPageVisibilitySupported,
      isMediaSessionSupported,
      autoPipEnabled,
      autoPipPermission,
      hasUserInteracted,
      participantCount,
      roomState: room?.state,
      localParticipant: !!localParticipant
    });
  }, []);

  return {
    togglePipMode,
    pipPortal,
    isSupported,
    controlPosition,
    // Auto PiP related exports
    autoPipEnabled,
    setAutoPipEnabled,
    autoPipPermission,
    isAutoPipEligible,
    requestAutoPipPermission,
    triggerAutoPip
  };
}