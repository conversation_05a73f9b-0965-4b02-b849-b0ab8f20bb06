/* eslint-disable no-unused-vars */
import React, { useState, useMemo, useCallback, useEffect } from "react";
import { List, Input, Collapse, Button } from "antd";
import { isMobileBrowser } from "@livekit/components-core";
import { ParticipantCard } from "./ParticipantCard";
import "../../styles/index.scss";
// import "./ParticipantList.scss";

import "./styles/ParticipantList.scss";
import SideDrawer from "../SideDrawer";
import { BreakoutRoomModal } from "./BreakoutRoomModal";
import { WaitingParticipantCard } from "./WaitingParticipantCard";
import { APIrequest } from "../../API/axios";
import { Endpoints } from "../../API/Endpoints/routes";
import { DrawerState } from "../../utils/constants";

const { Panel } = Collapse;
const { Search } = Input;

export function ParticipantList({
  remoteParticipants,
  localParticipant,
  setShowParticipantsList,
  showParticipantsList,
  id,
  isHost,
  layoutContext,
  isCoHost,
  lobbyParticipants,
  setLobbyParticipants,
  setRemoteRaisedHands,
  coHostToken,
  setDrawerState,
  breakoutRooms,
  setShowRaiseHand,
  currentRoomName,
  setBreakoutRoomDuration,
  meetingDetails,
  setBreakoutRooms,
  setRoomKeyCounter,
  isBreakoutRoomCnfigSet,
  setIsBreakoutRoomCnfigSet,
  meetingFeatures,
  setselectedprivatechatparticipant,
  setprivatechatparticipants,
  privatechatparticipants,
  setshowprivatechat,
  forcemute,
  forcevideooff,
  room,
  isBreakoutRoom,
  allowLiveCollabWhiteBoard,
  isWhiteboardOpen,
  isPinned,
  setIsPinned,
  setToastNotification,
  setToastStatus,
  setShowToast,
  participantColors = new Map(),
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isBreakoutRoomModalOpen, setIsBreakoutRoomModalOpen] = useState(false);
  const remote = Array.from(remoteParticipants.values());
  const remoteParticipantsArray = [localParticipant, ...remote];

  // Filter participants based on the search term
  const filteredParticipants = useMemo(() => {
    if (!searchTerm || searchTerm.trim() === "") return remoteParticipantsArray;
    return remoteParticipantsArray.filter((participant) =>
      participant.name.toLowerCase().includes(searchTerm.toLowerCase().trim())
    );
  }, [searchTerm, remoteParticipantsArray]);

  // On Search Function
  const onSearch = (value) => {
    setSearchTerm(value);
  };

  // Update lobbyParticipants periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime = Date.now();
      const timeLimit = 12 * 1000; // 12 seconds in milliseconds

      setLobbyParticipants((prev) => {
        const updatedMap = new Map(prev);
        Array.from(updatedMap.keys()).forEach((key) => {
          const participant = updatedMap.get(key);
          if (participant) {
            const timeDifference = currentTime - participant.time;
            if (timeDifference > timeLimit) {
              updatedMap.delete(key);
            }
          }
        });
        return updatedMap;
      });
    }, 2000); // Check every 2 second

    return () => clearInterval(interval); // Clean up the interval on component unmount
  }, [setLobbyParticipants, lobbyParticipants]);

  // Filter lobby participants based on time and map to required fields
  const filteredLobbyParticipants = useMemo(() => {
    const currentTime = Date.now();
    const timeLimit = 12 * 1000; // 12 seconds in milliseconds
    return Array.from(lobbyParticipants.values())
      .filter((participant) => currentTime - participant.time <= timeLimit)
      .map((participant) => ({
        display_name: participant.display_name,
        request_id: participant.request_id,
      }));
  }, [lobbyParticipants]);

  // Handle status update for all participants in lobby
  const handleStatus = useCallback(
    async (status) => {
      try {
        const response = await APIrequest({
          method: Endpoints.status_update_participant_lobby.method,
          endpoint: Endpoints.status_update_participant_lobby.url,
          payload: {
            meeting_uid: id,
            is_admit_all: status,
          },
          token: coHostToken,
        });
        if (response.success === 0) {
          setToastNotification("Error updating participant status");
          setToastStatus("error");
          setShowToast(true);
        } else {
          // Clear lobby participants on successful status update
          setLobbyParticipants(new Map());
          setToastNotification(status ? "All participants admitted" : "All participants denied");
          setToastStatus("success");
          setShowToast(true);
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");  
        setShowToast(true);
        // console.error("Error updating participant status", error);
      }
    },
    [id, setLobbyParticipants]
  );

  let activeRooms = [];
  useEffect(() => {
    activeRooms = Object.keys(breakoutRooms).filter(
      (key) =>
        !breakoutRooms[key].isDeleted || !("isDeleted" in breakoutRooms[key])
    );
    if (activeRooms.length > 1) {
      setIsBreakoutRoomCnfigSet(true);
    }
  }, [breakoutRooms]);

  const handleBreakoutRoomButton = () => {
    if (activeRooms.length > 1 || isBreakoutRoomCnfigSet) {
      setDrawerState(DrawerState.BREAKOUTROOM);
    } else {
      setIsBreakoutRoomModalOpen(true);
    }
  };

  return (
    <SideDrawer
      show={showParticipantsList}
      setShow={setShowParticipantsList}
      // title="Participants"
      title={currentRoomName === "Main Room" ? "Participants" : currentRoomName}
      isCoHost={isCoHost}
      isHost={isHost}
      localParticipant={localParticipant}
      setRemoteRaisedHands={setRemoteRaisedHands}
      setShowRaiseHand={setShowRaiseHand}
      count={remoteParticipantsArray.length}
      meetingFeatures={meetingFeatures}
      isWhiteboardOpen={isWhiteboardOpen}
      setDrawerState={setDrawerState}
    >
      <div className="pt-container-below">
        <Search
          placeholder="Enter name to search..."
          allowClear
          onSearch={onSearch}
          onChange={(e) => onSearch(e.target.value)}
          style={{ width: 200 }}
          className="pt-search"
        />
        {((isHost || isCoHost) && activeRooms.length > 1) ||
          (isBreakoutRoomCnfigSet && <div>{currentRoomName}</div>)}
        {isHost || isCoHost ? (
          filteredLobbyParticipants.length > 0 ? (
            <div className="pt-collapse">
              <Collapse defaultActiveKey={["1"]} accordion>
                <Panel
                  header={`Waiting in Lobby (${filteredLobbyParticipants.length})`}
                  key="1"
                  className="wil"
                >
                  <div className="waiting-in-lobby-buttons">
                    <Button type="text" onClick={() => handleStatus(false)}>
                      Deny All
                    </Button>
                    <Button type="text" onClick={() => handleStatus(true)}>
                      Allow All
                    </Button>
                  </div>
                  <List
                    dataSource={filteredLobbyParticipants}
                    renderItem={(participant) => (
                      <WaitingParticipantCard
                        participant={participant}
                        localParticipant={localParticipant}
                        raisedHand={false}
                        id={id}
                        isHost={isHost}
                        lobbyParticipants={lobbyParticipants}
                        setLobbyParticipants={setLobbyParticipants}
                        coHostToken={coHostToken}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        participantColors={participantColors}
                      />
                    )}
                  />
                </Panel>
                <Panel
                  header={`In Meeting (${filteredParticipants.length})`}
                  key="2"
                  className="inm"
                >
                  <List
                    dataSource={filteredParticipants}
                    renderItem={(participant) => (
                      <ParticipantCard
                        participant={participant}
                        localParticipant={localParticipant}
                        raisedHand={false}
                        id={id}
                        isHost={isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        coHostToken={coHostToken}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setselectedprivatechatparticipant
                        }
                        setprivatechatparticipants={setprivatechatparticipants}
                        privatechatparticipants={privatechatparticipants}
                        setDrawerState={setDrawerState}
                        setshowprivatechat={setshowprivatechat}
                        forcemute={forcemute}
                        forcevideooff={forcevideooff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        breakoutRooms={breakoutRooms}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        isPinned={isPinned}
                        setIsPinned={setIsPinned}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        participantColors={participantColors}
                      />
                    )}
                  />
                </Panel>
              </Collapse>
            </div>
          ) : (
            <List
              className="in-meeting-participants"
              dataSource={filteredParticipants}
              renderItem={(participant) => (
                <ParticipantCard
                  participant={participant}
                  localParticipant={localParticipant}
                  raisedHand={false}
                  id={id}
                  isHost={isHost}
                  layoutContext={layoutContext}
                  isCoHost={isCoHost}
                  coHostToken={coHostToken}
                  meetingFeatures={meetingFeatures}
                  setselectedprivatechatparticipant={
                    setselectedprivatechatparticipant
                  }
                  setprivatechatparticipants={setprivatechatparticipants}
                  privatechatparticipants={privatechatparticipants}
                  setDrawerState={setDrawerState}
                  setshowprivatechat={setshowprivatechat}
                  forcemute={forcemute}
                  forcevideooff={forcevideooff}
                  room={room}
                  isBreakoutRoom={isBreakoutRoom}
                  breakoutRooms={breakoutRooms}
                  allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                  isPinned={isPinned}
                  setIsPinned={setIsPinned}
                  setToastNotification={setToastNotification}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                  participantColors={participantColors}
                />
              )}
            />
          )
        ) : (
          <List
            dataSource={filteredParticipants}
            renderItem={(participant) => (
              <ParticipantCard
                participant={participant}
                localParticipant={localParticipant}
                raisedHand={false}
                id={id}
                isHost={isHost}
                layoutContext={layoutContext}
                isCoHost={isCoHost}
                coHostToken={coHostToken}
                meetingFeatures={meetingFeatures}
                setselectedprivatechatparticipant={
                  setselectedprivatechatparticipant
                }
                setprivatechatparticipants={setprivatechatparticipants}
                privatechatparticipants={privatechatparticipants}
                setDrawerState={setDrawerState}
                setshowprivatechat={setshowprivatechat}
                forcemute={forcemute}
                forcevideooff={forcevideooff}
                room={room}
                isBreakoutRoom={isBreakoutRoom}
                breakoutRooms={breakoutRooms}
                allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                isPinned={isPinned}
                setIsPinned={setIsPinned}
                setToastNotification={setToastNotification}
                setToastStatus={setToastStatus}
                setShowToast={setShowToast}
                participantColors={participantColors}
              />
            )}
          />
        )}
      </div>
      <BreakoutRoomModal
        isBreakoutRoomModalOpen={isBreakoutRoomModalOpen}
        setIsBreakoutRoomModalOpen={setIsBreakoutRoomModalOpen}
        setDrawerState={setDrawerState}
        breakoutRooms={breakoutRooms}
        remoteParticipantsArray={remoteParticipantsArray}
        meetingDetails={meetingDetails}
        setBreakoutRoomDuration={setBreakoutRoomDuration}
        setBreakoutRooms={setBreakoutRooms}
        setRoomKeyCounter={setRoomKeyCounter}
        setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
        coHostToken={coHostToken}
        localParticipant={localParticipant}
        setToastNotification={setToastNotification}
        setToastStatus={setToastStatus}
        setShowToast={setShowToast}
      />
      {!isMobileBrowser() &&
        meetingFeatures?.breakout_room === 1 &&
        (isHost || isCoHost) && (
          <div className="br-create-room">
            <Button
              type="primary"
              shape="round"
              onClick={() => {
                // setDrawerState(DrawerState.BREAKOUTROOM);
                handleBreakoutRoomButton();
              }}
            >
              {activeRooms.length > 1 || isBreakoutRoomCnfigSet
                ? "Go to Breakout Rooms"
                : "Create Breakout Rooms"}
            </Button>
          </div>
        )}
    </SideDrawer>
  );
}
