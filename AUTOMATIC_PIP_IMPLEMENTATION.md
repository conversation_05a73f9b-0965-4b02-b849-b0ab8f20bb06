# Automatic Picture-in-Picture Implementation

## Overview

This implementation adds automatic Picture-in-Picture (PiP) functionality to the Daakia video conferencing app, similar to how Google Meet works. The system automatically opens a PiP window when users switch tabs or minimize the browser window during an active video conference.

## Key Features

### ✅ Implemented Features

1. **Automatic PiP Triggering**
   - Detects when user switches tabs or minimizes window
   - 1-second delay to prevent accidental triggering
   - Auto-closes PiP when user returns to main tab

2. **Smart Eligibility Checking**
   - Requires active video conference with participants
   - Needs user interaction (click/touch) for browser activation
   - Checks if user is connected to room

3. **Permission Management**
   - Asks user consent before enabling auto PiP
   - Stores preference in localStorage
   - Respects user's choice (allow/deny)

4. **Browser Compatibility**
   - Document Picture-in-Picture API support detection
   - Page Visibility API integration
   - Media Session API for browser controls
   - Graceful fallback for unsupported browsers

5. **User Experience**
   - Toast notifications for auto PiP events
   - Debug console commands for testing
   - Status monitoring and controls

## Browser Support

### Supported Browsers
- **Chrome 116+** (Desktop) ✅
- **Edge 116+** (Desktop) ✅  
- **Opera 102+** (Desktop) ✅

### Required APIs
- **Document Picture-in-Picture API** (Primary)
- **Page Visibility API** (For tab detection)
- **Media Session API** (Optional, for browser controls)

### Unsupported Browsers
- Firefox (Document PiP not implemented yet)
- Safari (Document PiP not implemented yet)
- Mobile browsers (Document PiP desktop-only)

## Implementation Details

### Core Components

1. **usePictureInPicture Hook** (`PictureInPicture.js`)
   - Enhanced with auto PiP functionality
   - Page visibility monitoring
   - Permission management
   - Media session integration

2. **VideoConference Component** (`VideoConference.js`)
   - Integrates auto PiP hook
   - Exposes debug functions to window object

3. **AutoPipDemo Component** (`AutoPipDemo.js`)
   - Testing and demonstration interface
   - Status monitoring
   - Manual controls for debugging

### Key Functions

```javascript
// Check if auto PiP is eligible
isAutoPipEligible: boolean

// Trigger auto PiP manually
triggerAutoPip(): Promise<boolean>

// Request user permission
requestAutoPipPermission(): Promise<boolean>

// Toggle auto PiP on/off
setAutoPipEnabled(enabled: boolean): void
```

### Debug Console Commands

```javascript
// Check current status
window.daakiaAutoPip.status()

// Manually trigger PiP
window.daakiaAutoPip.trigger()

// Toggle control position
window.togglePipControlPosition()

// Enable/disable auto PiP
window.daakiaAutoPip.toggle(true/false)
```

## How It Works

### Automatic Triggering Process

1. **User joins video conference** with other participants
2. **User interacts** with the page (required for browser activation)
3. **System monitors** page visibility changes
4. **When user switches tabs:**
   - Page becomes hidden (document.hidden = true)
   - 1-second delay timer starts
   - If still hidden after delay, check eligibility
   - If eligible, check permission status
   - If permitted, automatically open PiP window
5. **When user returns to tab:**
   - Page becomes visible (document.hidden = false)
   - Automatically close PiP window
   - Show "welcome back" notification

### Permission Flow

1. **First time:** Ask user for permission with dialog
2. **User allows:** Store 'granted' in localStorage, enable auto PiP
3. **User denies:** Store 'denied' in localStorage, disable auto PiP
4. **Subsequent visits:** Use stored preference

### Eligibility Requirements

- ✅ Browser supports Document Picture-in-Picture API
- ✅ Page Visibility API is available
- ✅ Auto PiP is enabled by user
- ✅ User has interacted with the page
- ✅ Active video conference with participants
- ✅ User is connected to the room
- ✅ Permission is granted (or prompt for first time)

## Testing Instructions

### Manual Testing

1. **Join a video conference** with multiple participants
2. **Click anywhere** on the page to register user interaction
3. **Switch to another tab** or minimize the browser window
4. **Wait 1 second** - PiP should automatically open
5. **Return to the conference tab** - PiP should automatically close

### Using Demo Component

1. Import and render `AutoPipDemo` component
2. Use the status panel to monitor auto PiP state
3. Use control buttons to test functionality
4. Check browser console for debug information

### Debug Commands

```javascript
// Check if everything is working
window.daakiaAutoPip.status()
// Expected output: { supported: true, enabled: true, permission: 'granted', eligible: true }

// Test manual trigger
window.daakiaAutoPip.trigger()

// Toggle auto PiP
window.daakiaAutoPip.toggle(false) // disable
window.daakiaAutoPip.toggle(true)  // enable
```

## Configuration Options

### Hook Parameters

```javascript
usePictureInPicture({
  // ... existing parameters
  enableAutoPip: true, // Enable/disable auto PiP (default: true)
})
```

### Customizable Settings

- **Delay before triggering:** Currently 1 second (configurable)
- **Permission dialog text:** Customizable in `requestAutoPipPermission`
- **Notification messages:** Customizable in trigger functions
- **Eligibility conditions:** Modifiable in `isAutoPipEligible`

## Future Enhancements

### Potential Improvements

1. **Settings UI:** Add auto PiP toggle to settings menu
2. **Advanced Permissions:** Integration with browser's native permission API
3. **Smart Timing:** Adjust delay based on user behavior patterns
4. **Mobile Support:** When Document PiP becomes available on mobile
5. **Firefox/Safari Support:** When browsers implement Document PiP API

### Media Session Integration

The implementation includes Media Session API integration for:
- Browser media controls showing PiP button
- Automatic metadata for media session
- Enhanced browser integration

## Troubleshooting

### Common Issues

1. **Auto PiP not triggering:**
   - Check browser support: `window.daakiaAutoPip.status()`
   - Ensure user has interacted with page
   - Verify permission is granted
   - Check if in active video conference

2. **Permission dialog not showing:**
   - User may have already denied permission
   - Check localStorage: `localStorage.getItem('daakia-auto-pip-permission')`
   - Clear storage to reset: `localStorage.removeItem('daakia-auto-pip-permission')`

3. **PiP window not opening:**
   - Check browser console for errors
   - Verify Document PiP API support
   - Ensure no other PiP window is already open

### Reset Auto PiP Settings

```javascript
// Clear stored permission
localStorage.removeItem('daakia-auto-pip-permission');

// Reload page to reset state
window.location.reload();
```

## Comparison with Google Meet

### Similarities
- ✅ Automatic triggering on tab switch
- ✅ Permission-based system
- ✅ Auto-close when returning to tab
- ✅ User interaction requirement
- ✅ Smart eligibility checking

### Differences
- **Google Meet:** Uses video element PiP + Document PiP hybrid
- **Daakia:** Uses Document PiP exclusively for richer UI
- **Google Meet:** More complex permission UI
- **Daakia:** Simple confirm dialog (can be enhanced)

## Conclusion

This implementation provides a robust automatic Picture-in-Picture system that enhances user experience during video conferences. The system is designed to be respectful of user preferences while providing seamless functionality similar to Google Meet.

The implementation is production-ready with proper error handling, browser compatibility checks, and user consent management.
